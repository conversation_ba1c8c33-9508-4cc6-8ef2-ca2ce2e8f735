#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 WMZC性能优化模块
内存使用优化、缓存机制改进、性能监控
"""

import gc
import psutil
import time
import threading
import weakref
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
import asyncio

@dataclass
class MemoryStats:
    """内存统计信息"""
    total_mb: float = 0
    used_mb: float = 0
    available_mb: float = 0
    percent: float = 0
    process_mb: float = 0

@dataclass
class CacheStats:
    """缓存统计信息"""
    hit_count: int = 0
    miss_count: int = 0
    total_size: int = 0
    memory_usage_mb: float = 0
    hit_rate: float = 0

class AdvancedCacheManager:
    """高级缓存管理器"""
    
    def __init__(self, max_memory_mb: float = 100):
        self.max_memory_mb = max_memory_mb
        self.caches = {}
        self.cache_metadata = {}
        self.access_times = defaultdict(deque)
        self.lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'memory_cleanups': 0
        }
    
    def get(self, cache_name: str, key: str) -> Any:
        """获取缓存项"""
        with self.lock:
            if cache_name not in self.caches:
                self.stats['misses'] += 1
                return None
            
            cache = self.caches[cache_name]
            if key not in cache:
                self.stats['misses'] += 1
                return None
            
            # 检查TTL
            metadata = self.cache_metadata.get(cache_name, {}).get(key)
            if metadata and 'ttl' in metadata:
                if time.time() - metadata['timestamp'] > metadata['ttl']:
                    del cache[key]
                    if key in self.cache_metadata.get(cache_name, {}):
                        del self.cache_metadata[cache_name][key]
                    self.stats['misses'] += 1
                    return None
            
            # 更新访问时间
            self.access_times[cache_name].append((key, time.time()))
            if len(self.access_times[cache_name]) > 1000:
                self.access_times[cache_name].popleft()
            
            self.stats['hits'] += 1
            return cache[key]
    
    def set(self, cache_name: str, key: str, value: Any, ttl: Optional[int] = None):
        """设置缓存项"""
        with self.lock:
            # 初始化缓存
            if cache_name not in self.caches:
                self.caches[cache_name] = {}
                self.cache_metadata[cache_name] = {}
            
            # 检查内存使用
            if self._get_memory_usage_mb() > self.max_memory_mb:
                self._cleanup_memory()
            
            # 设置缓存
            self.caches[cache_name][key] = value
            
            # 设置元数据
            metadata = {
                'timestamp': time.time(),
                'size': self._estimate_size(value)
            }
            if ttl:
                metadata['ttl'] = ttl
            
            self.cache_metadata[cache_name][key] = metadata
    
    def delete(self, cache_name: str, key: str):
        """删除缓存项"""
        with self.lock:
            if cache_name in self.caches and key in self.caches[cache_name]:
                del self.caches[cache_name][key]
            if cache_name in self.cache_metadata and key in self.cache_metadata[cache_name]:
                del self.cache_metadata[cache_name][key]
    
    def clear_cache(self, cache_name: str):
        """清空指定缓存"""
        with self.lock:
            if cache_name in self.caches:
                self.caches[cache_name].clear()
            if cache_name in self.cache_metadata:
                self.cache_metadata[cache_name].clear()
    
    def cleanup_expired(self) -> int:
        """清理过期缓存"""
        cleaned_count = 0
        current_time = time.time()
        
        with self.lock:
            for cache_name in list(self.caches.keys()):
                cache = self.caches[cache_name]
                metadata_cache = self.cache_metadata.get(cache_name, {})
                
                expired_keys = []
                for key, metadata in metadata_cache.items():
                    if 'ttl' in metadata:
                        if current_time - metadata['timestamp'] > metadata['ttl']:
                            expired_keys.append(key)
                
                for key in expired_keys:
                    if key in cache:
                        del cache[key]
                    if key in metadata_cache:
                        del metadata_cache[key]
                    cleaned_count += 1
        
        return cleaned_count
    
    def _cleanup_memory(self):
        """🔧 P1级别Bug修复: 改进内存清理策略，提高效率"""
        cleanup_start_time = time.time()
        cleaned_items = 0

        with self.lock:
            try:
                # 首先清理过期项目
                expired_cleaned = self.cleanup_expired()
                cleaned_items += expired_cleaned

                # 检查是否仍需要清理
                current_memory = self._get_memory_usage_mb()
                target_memory = self.max_memory_mb * 0.7  # 清理到70%

                if current_memory <= target_memory:
                    return  # 不需要进一步清理

                # 🔧 P1修复: 改进LRU清理策略
                # 按缓存大小排序，优先清理大缓存
                cache_sizes = []
                for cache_name in self.caches:
                    cache_size = len(self.caches[cache_name])
                    if cache_size > 0:
                        cache_sizes.append((cache_name, cache_size))

                # 按大小降序排序
                cache_sizes.sort(key=lambda x: x[1], reverse=True)

                # 清理策略：从大缓存开始清理
                for cache_name, cache_size in cache_sizes:
                    if self._get_memory_usage_mb() <= target_memory:
                        break

                    if cache_name in self.access_times:
                        access_queue = self.access_times[cache_name]

                        # 批量清理，提高效率
                        batch_size = min(50, len(access_queue) // 4)  # 每次清理1/4或最多50个
                        items_to_remove = []

                        # 收集要删除的项目
                        for _ in range(batch_size):
                            if len(access_queue) == 0:
                                break
                            key, _ = access_queue.popleft()
                            if key in self.caches[cache_name]:
                                items_to_remove.append(key)

                        # 批量删除
                        for key in items_to_remove:
                            if key in self.caches[cache_name]:
                                del self.caches[cache_name][key]
                            if cache_name in self.cache_metadata and key in self.cache_metadata[cache_name]:
                                del self.cache_metadata[cache_name][key]
                            cleaned_items += 1
                            self.stats['evictions'] += 1

                # 强制垃圾回收
                import gc
                gc.collect()

                self.stats['memory_cleanups'] += 1

                # 记录清理效果
                cleanup_time = time.time() - cleanup_start_time
                final_memory = self._get_memory_usage_mb()

                if cleanup_time > 0.1:  # 清理时间超过100ms时记录
                    print(f"🧹 内存清理完成: 清理{cleaned_items}项, "
                          f"内存从{current_memory:.1f}MB降至{final_memory:.1f}MB, "
                          f"耗时{cleanup_time:.3f}s")

            except Exception as e:
                print(f"❌ 内存清理异常: {e}")
                self.stats['cleanup_errors'] = self.stats.get('cleanup_errors', 0) + 1
    
    def _get_memory_usage_mb(self) -> float:
        """估算缓存内存使用量"""
        total_size = 0
        for cache in self.caches.values():
            for value in cache.values():
                total_size += self._estimate_size(value)
        return total_size / (1024 * 1024)
    
    def _estimate_size(self, obj, visited=None) -> int:
        """🔧 P1级别Bug修复: 改进内存估算，防止无限递归和性能问题"""
        if visited is None:
            visited = set()

        try:
            import sys

            # 防止循环引用导致的无限递归
            obj_id = id(obj)
            if obj_id in visited:
                return 0
            visited.add(obj_id)

            # 基础大小
            size = sys.getsizeof(obj)

            # 🔧 P1修复: 限制递归深度，避免性能问题
            if len(visited) > 50:  # 限制递归深度
                return size

            # 递归计算容器对象的实际大小
            if isinstance(obj, dict):
                # 限制字典大小估算，避免大字典导致性能问题
                if len(obj) > 1000:
                    # 对于大字典，采样估算
                    sample_items = list(obj.items())[:100]
                    # 🔧 P0-3修复：使用共享visited集合，避免循环引用检测失效
                    sample_size = sum(sys.getsizeof(k) + self._estimate_size(v, visited)
                                    for k, v in sample_items)
                    size += (sample_size * len(obj)) // 100
                else:
                    # 🔧 P0-3修复：使用共享visited集合，避免循环引用检测失效
                    size += sum(sys.getsizeof(k) + self._estimate_size(v, visited)
                              for k, v in obj.items())

            elif isinstance(obj, (list, tuple)):
                # 限制列表/元组大小估算
                if len(obj) > 1000:
                    # 对于大列表，采样估算
                    sample_items = obj[:100]
                    # 🔧 P0-3修复：使用共享visited集合，避免循环引用检测失效
                    sample_size = sum(self._estimate_size(item, visited) for item in sample_items)
                    size += (sample_size * len(obj)) // 100
                else:
                    # 🔧 P0-3修复：使用共享visited集合，避免循环引用检测失效
                    size += sum(self._estimate_size(item, visited) for item in obj)

            elif isinstance(obj, set):
                # 集合类型的估算
                if len(obj) > 1000:
                    sample_items = list(obj)[:100]
                    # 🔧 P0-3修复：使用共享visited集合，避免循环引用检测失效
                    sample_size = sum(self._estimate_size(item, visited) for item in sample_items)
                    size += (sample_size * len(obj)) // 100
                else:
                    # 🔧 P0-3修复：使用共享visited集合，避免循环引用检测失效
                    size += sum(self._estimate_size(item, visited) for item in obj)

            elif hasattr(obj, '__dict__') and not isinstance(obj, type):
                # 对于自定义对象，估算其属性的大小（排除类型对象）
                try:
                    obj_dict = obj.__dict__
                    if len(obj_dict) <= 50:  # 限制属性数量
                        # 🔧 P0-3修复：使用共享visited集合，避免循环引用检测失效
                        size += sum(sys.getsizeof(k) + self._estimate_size(v, visited)
                                   for k, v in obj_dict.items())
                    else:
                        # 对于属性很多的对象，使用保守估计
                        size += len(obj_dict) * 100  # 每个属性估算100字节
                except (AttributeError, TypeError):
                    pass

            return size

        except (RecursionError, MemoryError):
            # 递归过深或内存不足时，返回保守估计
            return 1024
        except Exception:
            # 其他异常，返回保守估计
            return 512
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计"""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
        
        return CacheStats(
            hit_count=self.stats['hits'],
            miss_count=self.stats['misses'],
            total_size=sum(len(cache) for cache in self.caches.values()),
            memory_usage_mb=self._get_memory_usage_mb(),
            hit_rate=hit_rate
        )

class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, warning_threshold: float = 80.0, critical_threshold: float = 90.0):
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.monitoring = False
        self.monitor_thread = None
        self.callbacks = []
        
        # 历史数据
        self.memory_history = deque(maxlen=100)
        self.gc_history = deque(maxlen=50)
    
    def start_monitoring(self, interval: float = 30.0):
        """开始内存监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止内存监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
    
    def add_callback(self, callback):
        """添加内存警告回调"""
        self.callbacks.append(callback)
    
    def get_memory_stats(self) -> MemoryStats:
        """获取内存统计"""
        try:
            # 系统内存
            memory = psutil.virtual_memory()
            
            # 进程内存
            process = psutil.Process()
            process_memory = process.memory_info()
            
            return MemoryStats(
                total_mb=memory.total / (1024 * 1024),
                used_mb=memory.used / (1024 * 1024),
                available_mb=memory.available / (1024 * 1024),
                percent=memory.percent,
                process_mb=process_memory.rss / (1024 * 1024)
            )
        except Exception:
            return MemoryStats()
    
    def should_cleanup(self) -> bool:
        """判断是否需要清理内存"""
        stats = self.get_memory_stats()
        return stats.percent > self.warning_threshold
    
    def cleanup_memory(self):
        """执行内存清理"""
        gc_start = time.time()
        
        # 强制垃圾回收
        collected = gc.collect()
        
        gc_duration = time.time() - gc_start
        self.gc_history.append({
            'timestamp': time.time(),
            'collected': collected,
            'duration': gc_duration
        })
        
        return collected
    
    def _monitor_loop(self, interval: float):
        """监控循环"""
        while self.monitoring:
            try:
                stats = self.get_memory_stats()
                self.memory_history.append({
                    'timestamp': time.time(),
                    'stats': stats
                })
                
                # 检查阈值
                if stats.percent > self.critical_threshold:
                    self._trigger_callbacks('critical', stats)
                elif stats.percent > self.warning_threshold:
                    self._trigger_callbacks('warning', stats)
                
                time.sleep(interval)
            except Exception as e:
                print(f"内存监控错误: {e}")
                time.sleep(interval)
    
    def _trigger_callbacks(self, level: str, stats: MemoryStats):
        """触发回调函数"""
        for callback in self.callbacks:
            try:
                callback(level, stats)
            except Exception as e:
                print(f"内存监控回调错误: {e}")

class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.profiles = defaultdict(list)
        self.active_profiles = {}
        self.lock = threading.Lock()
    
    def start_profile(self, name: str):
        """开始性能分析"""
        with self.lock:
            self.active_profiles[name] = time.time()
    
    def end_profile(self, name: str) -> float:
        """结束性能分析"""
        with self.lock:
            if name not in self.active_profiles:
                return 0
            
            duration = time.time() - self.active_profiles[name]
            del self.active_profiles[name]
            
            self.profiles[name].append({
                'duration': duration,
                'timestamp': time.time()
            })
            
            # 保持最近100次记录
            if len(self.profiles[name]) > 100:
                self.profiles[name].pop(0)
            
            return duration
    
    def get_profile_stats(self, name: str) -> Dict:
        """获取性能统计"""
        with self.lock:
            if name not in self.profiles or not self.profiles[name]:
                return {}
            
            durations = [p['duration'] for p in self.profiles[name]]
            return {
                'count': len(durations),
                'avg': sum(durations) / len(durations),
                'min': min(durations),
                'max': max(durations),
                'total': sum(durations)
            }
    
    def get_all_stats(self) -> Dict:
        """获取所有性能统计"""
        return {name: self.get_profile_stats(name) for name in self.profiles}

class PerformanceOptimizer:
    """性能优化器主类"""
    
    def __init__(self):
        self.cache_manager = AdvancedCacheManager()
        self.memory_monitor = MemoryMonitor()
        self.profiler = PerformanceProfiler()
        
        # 注册内存监控回调
        self.memory_monitor.add_callback(self._handle_memory_warning)
    
    def start(self):
        """启动性能优化器"""
        self.memory_monitor.start_monitoring()
        print("🚀 性能优化器已启动")
    
    def stop(self):
        """停止性能优化器"""
        self.memory_monitor.stop_monitoring()
        print("🛑 性能优化器已停止")
    
    def _handle_memory_warning(self, level: str, stats: MemoryStats):
        """处理内存警告"""
        if level == 'critical':
            print(f"🚨 内存使用率过高: {stats.percent:.1f}%")
            # 执行紧急清理
            self.cache_manager.cleanup_expired()
            self.memory_monitor.cleanup_memory()
        elif level == 'warning':
            print(f"⚠️ 内存使用率警告: {stats.percent:.1f}%")
            # 执行常规清理
            self.cache_manager.cleanup_expired()
    
    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        return {
            'memory': self.memory_monitor.get_memory_stats().__dict__,
            'cache': self.cache_manager.get_stats().__dict__,
            'profiles': self.profiler.get_all_stats(),
            'timestamp': time.time()
        }

# 全局性能优化器实例
performance_optimizer = PerformanceOptimizer()

# 装饰器函数
def profile_performance(name: str):
    """性能分析装饰器"""
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                performance_optimizer.profiler.start_profile(name)
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    performance_optimizer.profiler.end_profile(name)
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                performance_optimizer.profiler.start_profile(name)
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    performance_optimizer.profiler.end_profile(name)
            return sync_wrapper
    return decorator

def cached(cache_name: str, ttl: Optional[int] = None):
    """缓存装饰器 - 🔧 Bug修复: 改进缓存键生成"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 🔧 Bug修复: 使用更稳定的缓存键生成方法
            try:
                import hashlib
                # 使用MD5哈希确保一致性
                args_str = str(args) if args else ""
                kwargs_str = str(sorted(kwargs.items())) if kwargs else ""
                content = f"{func.__name__}_{args_str}_{kwargs_str}"
                cache_key = hashlib.md5(content.encode('utf-8')).hexdigest()
            except Exception:
                # 如果哈希生成失败，使用简单的字符串键
                cache_key = f"{func.__name__}_{len(args)}_{len(kwargs)}"

            # 尝试从缓存获取
            result = performance_optimizer.cache_manager.get(cache_name, cache_key)
            if result is not None:
                return result

            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            performance_optimizer.cache_manager.set(cache_name, cache_key, result, ttl)

            return result
        return wrapper
    return decorator
